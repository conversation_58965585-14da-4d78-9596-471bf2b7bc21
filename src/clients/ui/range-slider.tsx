import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import cn from "classnames";

type Props<T extends number | readonly number[]> = {
  showThumbValue?: boolean;
  value?: T;
  defaultValue?: T;
  min?: number;
  max?: number;
  step?: number;
  orientation?: "horizontal" | "vertical";
  disabled?: boolean;
  className?: string;
  onChange?: (value: T) => void;
  onBeforeChange?: (value: T) => void;
  onAfterChange?: (value: T) => void;
  minDistance?: number;
  marks?: boolean | number[];
  included?: boolean;
  withTracks?: boolean;
  invert?: boolean;
};

const RangeSlider = <T extends number | readonly number[]>({
  showThumbValue = false,
  value,
  defaultValue,
  min = 0,
  max = 100,
  step = 1,
  orientation = "horizontal",
  disabled = false,
  className = "",
  onChange,
  onBeforeChange,
  onAfterChange,
  minDistance = 0,
  marks = false,
  included = true,
  withTracks = true,
  invert = false,
  ...props
}: Props<T>) => {
  const isVertical = orientation === "vertical";
  const sliderRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<number | null>(null);
  const [internalValue, setInternalValue] = useState<T>(() => {
    return value ?? defaultValue ?? (0 as T);
  });

  // Update internal value when prop value changes
  useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  const currentValue = value !== undefined ? value : internalValue;
  const isArray = Array.isArray(currentValue);

  const values = useMemo(() => {
    return isArray ? (currentValue as number[]) : [currentValue as number];
  }, [currentValue, isArray]);

  const getPercentage = useCallback(
    (val: number) => {
      return ((val - min) / (max - min)) * 100;
    },
    [min, max]
  );

  const getValueFromPosition = useCallback(
    (position: number, sliderSize: number) => {
      const percentage = Math.max(
        0,
        Math.min(100, (position / sliderSize) * 100)
      );
      const rawValue = min + (percentage / 100) * (max - min);
      const steppedValue = Math.round(rawValue / step) * step;
      return Math.max(min, Math.min(max, steppedValue));
    },
    [min, max, step]
  );

  const handleMouseDown = useCallback(
    (thumbIndex: number) => (e: React.MouseEvent) => {
      if (disabled) return;
      e.preventDefault();
      setIsDragging(thumbIndex);
      onBeforeChange?.(currentValue);
    },
    [disabled, onBeforeChange, currentValue]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging === null || !sliderRef.current) return;

      const rect = sliderRef.current.getBoundingClientRect();
      const position = isVertical
        ? invert
          ? e.clientY - rect.top
          : rect.bottom - e.clientY
        : invert
        ? rect.right - e.clientX
        : e.clientX - rect.left;
      const sliderSize = isVertical ? rect.height : rect.width;

      const newValue = getValueFromPosition(position, sliderSize);

      if (isArray) {
        const newValues = [...values];
        newValues[isDragging] = newValue;

        // Apply minDistance constraint
        if (minDistance > 0) {
          if (isDragging === 0 && newValues[1] !== undefined) {
            newValues[0] = Math.min(newValues[0], newValues[1] - minDistance);
          } else if (isDragging === 1 && newValues[0] !== undefined) {
            newValues[1] = Math.max(newValues[1], newValues[0] + minDistance);
          }
        }

        const updatedValue = newValues as unknown as T;
        setInternalValue(updatedValue);
        onChange?.(updatedValue);
      } else {
        const updatedValue = newValue as T;
        setInternalValue(updatedValue);
        onChange?.(updatedValue);
      }
    },
    [
      isDragging,
      isVertical,
      invert,
      getValueFromPosition,
      isArray,
      values,
      minDistance,
      onChange,
    ]
  );

  const handleMouseUp = useCallback(() => {
    if (isDragging !== null) {
      setIsDragging(null);
      onAfterChange?.(currentValue);
    }
  }, [isDragging, onAfterChange, currentValue]);

  useEffect(() => {
    if (isDragging !== null) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const renderThumbs = () => {
    return values.map((val, index) => {
      const percentage = getPercentage(val);
      const style = isVertical
        ? { [invert ? "top" : "bottom"]: `${percentage}%` }
        : { [invert ? "right" : "left"]: `${percentage}%` };

      return (
        <div
          key={index}
          className={cn({
            "absolute w-5 h-5 rounded-full bg-primary-blue text-xs text-white flex items-center justify-center cursor-grab hover:shadow-sliderThumb transform -translate-x-1/2 -translate-y-1/2":
              true,
            "cursor-grabbing": isDragging === index,
            "cursor-not-allowed opacity-50": disabled,
          })}
          style={style}
          onMouseDown={handleMouseDown(index)}
        >
          {showThumbValue && val}
        </div>
      );
    });
  };

  const renderTracks = () => {
    if (!withTracks) return null;

    if (isArray && values.length === 2) {
      const [start, end] = values;
      const startPercentage = getPercentage(start);
      const endPercentage = getPercentage(end);

      return (
        <>
          {/* Background track */}
          <div
            className={cn({
              "absolute rounded-full bg-primary-blue bg-opacity-30": true,
              "h-1.5 w-full top-1/2 -translate-y-1/2": !isVertical,
              "w-1.5 h-full left-1/2 -translate-x-1/2": isVertical,
            })}
          />
          {/* Active track */}
          <div
            className={cn({
              "absolute rounded-full bg-primary-blue": true,
              "h-1.5 top-1/2 -translate-y-1/2": !isVertical,
              "w-1.5 left-1/2 -translate-x-1/2": isVertical,
            })}
            style={
              isVertical
                ? {
                    [invert ? "top" : "bottom"]: `${Math.min(
                      startPercentage,
                      endPercentage
                    )}%`,
                    height: `${Math.abs(endPercentage - startPercentage)}%`,
                  }
                : {
                    [invert ? "right" : "left"]: `${Math.min(
                      startPercentage,
                      endPercentage
                    )}%`,
                    width: `${Math.abs(endPercentage - startPercentage)}%`,
                  }
            }
          />
        </>
      );
    } else {
      // Single value slider
      const percentage = getPercentage(values[0]);

      return (
        <>
          {/* Background track */}
          <div
            className={cn({
              "absolute rounded-full bg-primary-blue bg-opacity-30": true,
              "h-1.5 w-full top-1/2 -translate-y-1/2": !isVertical,
              "w-1.5 h-full left-1/2 -translate-x-1/2": isVertical,
            })}
          />
          {/* Active track */}
          {included && (
            <div
              className={cn({
                "absolute rounded-full bg-primary-blue": true,
                "h-1.5 top-1/2 -translate-y-1/2": !isVertical,
                "w-1.5 left-1/2 -translate-x-1/2": isVertical,
              })}
              style={
                isVertical
                  ? {
                      [invert ? "top" : "bottom"]: "0%",
                      height: `${percentage}%`,
                    }
                  : {
                      [invert ? "right" : "left"]: "0%",
                      width: `${percentage}%`,
                    }
              }
            />
          )}
        </>
      );
    }
  };

  const renderMarks = () => {
    if (!marks) return null;

    const markValues = Array.isArray(marks) ? marks : [];
    if (markValues.length === 0) return null;

    return markValues.map((mark, index) => {
      const percentage = getPercentage(mark);
      const style = isVertical
        ? { [invert ? "top" : "bottom"]: `${percentage}%` }
        : { [invert ? "right" : "left"]: `${percentage}%` };

      return (
        <div
          key={index}
          className={cn({
            "absolute w-5 h-5 rounded-full bg-primary-blue transform -translate-x-1/2 -translate-y-1/2":
              true,
          })}
          style={style}
        />
      );
    });
  };

  return (
    <div
      ref={sliderRef}
      className={cn(
        {
          "relative touch-none select-none": true,
          "w-full h-10": !isVertical,
          "h-full w-10": isVertical,
          "cursor-not-allowed": disabled,
        },
        className
      )}
      {...props}
    >
      {renderTracks()}
      {renderThumbs()}
      {renderMarks()}
    </div>
  );
};

export default RangeSlider;

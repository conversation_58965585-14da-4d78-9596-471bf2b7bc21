"use client";

import RangeSlider from "@/clients/ui/range-slider";
import Image from "next/image";
import { useCallback, useState } from "react";

const RentFilter = () => {
  const [showPrice, setShowPrice] = useState(false);

  const onToggle = useCallback(() => {
    setShowPrice((_p) => !_p);
  }, []);

  return (
    <>
      <div
        className="flex items-center gap-x-2 p-2 border rounded w-[220px] text-sm"
        onClick={onToggle}
      >
        <Image
          alt="rent filter icon"
          src="/images/icons/price.svg"
          width={20}
          height={20}
        />
        Price Range
      </div>
      {showPrice && <RangeSlider />}
    </>
  );
};

export default RentFilter;

"use client";

import Button from "@/clients/ui/button";
import { useState } from "react";
import RentFilter from "./Filters/RentFilter";

const PropertiesFilter = () => {
  // Form state
  const [formData, setFormData] = useState({
    dates: [],
    last_update: "",
    area: [],
    key_number: "",
    owner_last_name: "",
    nr_listing_id: "",
    nights: "",
    capacity_gte: "",
    min_price: null,
    max_price: null,
    address: "",
    show_inactive: false,
    show_without_price: false,
    bedroom_num_gte: null,
    air_conditioning_central: false,
    air_conditioning_minisplit: false,
    air_conditioning_windowunits: false,
    pets_askowner: false,
    pets_yes: false,
    pets_no: false,
    pool_private: false,
    pool_community: false,
    walk_to_beach: false,
    waterfront: false,
    water_views: false,
  });

  // UI state

  const [showAmenities, setShowAmenities] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 30000]);
  return (
    <div className="flex w-full gap-x-4">
      <div className="w-[80%]">
        <RentFilter />
      </div>
      <div className="w-[20%] flex items-center flex-col gap-y-2">
        <Button className="w-full" intent="outline">
          Clear Filter
        </Button>
        <Button className="w-full">Add A Listing</Button>
      </div>
    </div>
  );
};

export default PropertiesFilter;

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer components {
  .flex-center-center {
    @apply flex items-center justify-center;
  }

  .flex-center-between {
    @apply flex items-center justify-between;
  }

  .flex-center {
    @apply flex items-center;
  }
  .fluid-container {
    @apply container max-w-full p-4 md:p-6 xl:p-8;
  }
}

.requires-no-scroll {
  @media screen and (min-width: 728px) {
    overflow: hidden;
    padding-right: 8px;
  }
}

.react-datepicker__time-list-item--selected {
  background-color: #15a5e5;
  color: white;
}

.ql-snow {
  .ql-picker {
    &.ql-size {
      .ql-picker-label,
      .ql-picker-item {
        &::before {
          content: attr(data-value) !important;
        }
      }
    }
  }
}

.ql-snow .ql-editor .ql-code-block {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 5px;
  font-family: "Courier New", Courier, monospace;
  font-size: 14px;
  overflow-x: auto;
}

.ql-align-center {
  text-align: center !important;
}

.ql-align-right {
  text-align: right !important;
}

.ql-align-justify {
  text-align: justify !important;
}

.ql-align-left {
  text-align: left !important;
}
